/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable, InjectionToken, Injector } from '@angular/core';

import { ServiceRole, IDataServiceOptions, IDataServiceEndPointOptions, IDataServiceChildRoleOptions, DataServiceFlatNode } from '@libs/platform/data-access';
import { IBasicsEfbsheetsEntity, BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN, childType, IEstNonwageCostsEntity } from '@libs/basics/interfaces';
import { BasicsEfbsheetsDataService } from './basics-efbsheets-data.service';
import { PlatformLazyInjectorService } from '@libs/platform/common';
import { IBasicsEfbsheetNonWageComplete } from '../model/entities/basics-efsheets-nonwage-complete.interface';
import { IBasicsEfbsheetsComplete } from '../model/entities/basics-efbsheets-complete.interface';
export const BASICS_EFBSHEETS_NONWAGE_COSTS_DATA_TOKEN = new InjectionToken<BasicsEfbsheetsNonwageCostsDataService>('basicsEfbsheetsNonwageCostsDataToken');

@Injectable({
	providedIn: 'root'
})

/**
 * @class BasicsEfbsheetsNonwageCostsDataService
 */
export class BasicsEfbsheetsNonwageCostsDataService extends DataServiceFlatNode<IEstNonwageCostsEntity, IBasicsEfbsheetNonWageComplete, IBasicsEfbsheetsEntity, IBasicsEfbsheetsComplete> {
	private readonly parentService = inject(BasicsEfbsheetsDataService);
	private readonly lazyInjector = inject(PlatformLazyInjectorService);
	public constructor(basicsEfbsheetsDataService: BasicsEfbsheetsDataService, private readonly injector: Injector) {

		const options: IDataServiceOptions<IEstNonwageCostsEntity> = {
			apiUrl: 'basics/efbsheets/nonwagecosts',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: true
			},
			updateInfo: <IDataServiceEndPointOptions>{
				endPoint: 'update',
				usePost: true
			},
			roleInfo: <IDataServiceChildRoleOptions<IEstNonwageCostsEntity, IBasicsEfbsheetsEntity, IBasicsEfbsheetsComplete>>{
				role: ServiceRole.Node,
				itemName: 'EstNonwageCosts',
				parent: basicsEfbsheetsDataService
			},
			createInfo: <IDataServiceEndPointOptions>{
				endPoint: 'create'
			}
		};

		super(options);
	}

	/**
	 * @brief Creates a complete wrapper object for a modified non-wage cost entity.
	 * @param modified The modified non-wage cost entity to wrap, or `null` if none.
	 * @returns A new instance of `BasicsEfbsheetNonWageComplete` containing the modified entity if provided.
	 */
	public override createUpdateEntity(modified: IEstNonwageCostsEntity | null): IBasicsEfbsheetNonWageComplete {
		return {
			MainItemId: modified?.Id ,
			EstNonwageCosts: modified ?? null
		} as IBasicsEfbsheetNonWageComplete;
	}

	/**
	 * This method always returns `true`, indicating that registration by method is enable
	 * @returns
	 */
	public override registerByMethod(): boolean {
		return true;
	}
	
     /**
	 * @brief Retrieves the saved completes from the update process.
	 * @param complete The parent object (`IBasicsEfbsheetsComplete`) that holds the full update state.
	 * @returns An array of `BasicsEfbsheetNonWageComplete` objects that have been saved.
	 */
	public override getSavedCompletesFromUpdate(parentUpdate: IBasicsEfbsheetsComplete): IBasicsEfbsheetNonWageComplete[] {
		if (parentUpdate && parentUpdate.EstNonwageCostsToSave) {
			return parentUpdate.EstNonwageCostsToSave.map(entity => ({
				EntitiesCount: 1,
				Id: entity.Id ?? 0,
				MainItemId: entity.Id ?? 0,
				EstNonwageCosts: entity
			} as IBasicsEfbsheetNonWageComplete));
		}
		return [];
	}
	
	/**
	 * @brief Registers modified and deleted non-wage cost entities to the parent update object.
	 * @param complete The parent object (`IBasicsEfbsheetsComplete`) that holds the full update state.
	 * @param modified An array of modified `BasicsEfbsheetNonWageComplete` entities to be saved.
	 * @param deleted An array of deleted `IEstNonwageCostsEntity` entries to be removed.
	 */
	public override registerNodeModificationsToParentUpdate(complete: IBasicsEfbsheetsComplete, modified: IBasicsEfbsheetNonWageComplete[], deleted: IEstNonwageCostsEntity[]) {
		if (modified && modified.length > 0) {
			complete.EstNonwageCostsToSave = modified
				.map(item => item.EstNonwageCosts)
				.filter((entity): entity is IEstNonwageCostsEntity => entity !== undefined);
		}
		if (deleted && deleted.length > 0) {
			complete.EstNonwageCostsToDelete = deleted;
		}
	}

    /**
	 * @brief Retrieves the saved entities from the update process.
	 * @param complete The parent object (`IBasicsEfbsheetsComplete`) that holds the full update state.
	 * @returns An array of `IEstNonwageCostsEntity` objects that have been saved.
	 */
	public override getSavedEntitiesFromUpdate(complete: IBasicsEfbsheetsComplete): IEstNonwageCostsEntity[] {
		return complete?.EstNonwageCostsToSave ?? [];
	}

	/**
	 * Determines if the given entity is a child of the specified parent entity
	 * @param parentKey
	 * @param entity
	 * @returns
	 */
	public override isParentFn(parentKey: IBasicsEfbsheetsEntity, entity: IEstNonwageCostsEntity): boolean {
		return entity.EstCrewmixFk === parentKey.Id;
	}

	/**
	 * Provides the payload for loading entities.
	 * @returns An object representing the payload for loading entities.
	 */
	protected override provideLoadPayload(): object {
		const parentSelection = this.getSelectedParent();
		if (parentSelection) {
			return {
				estCrewMixFk: parentSelection.Id
			};
		}
		return {};
	}

	/**
	 * Provides the payload for creating a new entity.
	 * @returns An object representing the payload for creating a new entity.
	 */
	protected override provideCreatePayload(): object {
		const parentSelection = this.getSelectedParent();
		if (parentSelection) {
			return {
				EstCrewmixFk: parentSelection.Id
			};
		}
		return {};
	}

	/**
	 * Handles the successful creation of an entity by casting and returning it.
	 * @param created
	 * @returns
	 */
	public override onCreateSucceeded(created: object): IEstNonwageCostsEntity {
		return created as IEstNonwageCostsEntity;
	}

	/**
	 *  Handles the change event for a specific field in an entity.
	 * @param field
	 * @param entity
	 * @returns This method does not return anything
	 */
	public async fieldChangeForMaster(entity: IEstNonwageCostsEntity, field: string, value: number): Promise<void> {
		const selectedCrewMix = this.parentService.getSelectedEntity();
		if (!selectedCrewMix) {
			return;
		}

		switch (field) {
			case 'Count':
				entity.Count = value;
				break;
			case 'RateDay':
				entity.RateDay = value;
				break;
			default:
				break;
		}

		if (['Count', 'RateDay', 'MdcWageGroupFk'].includes(field)) {
			const basicsCommonToken = this.lazyInjector.inject(BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN);
			const basicsCommon = await basicsCommonToken;
			basicsCommon.calculateCrewmixesAndChilds(selectedCrewMix, childType.NonwageCosts, true);
			this.parentService.setModified(selectedCrewMix);
		}
	}
}


